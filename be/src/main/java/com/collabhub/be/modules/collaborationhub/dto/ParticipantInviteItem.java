package com.collabhub.be.modules.collaborationhub.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import org.jooq.generated.enums.HubParticipantRole;

import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * DTO representing a single participant invitation item.
 * Used within HubParticipantInviteRequest to specify individual participants to invite.
 * Validates that required fields are present based on participant type.
 */
public class ParticipantInviteItem {

    @NotBlank(message = "Participant type is required")
    private String type; // "internal", "external", "brand_contact"

    @JsonProperty("user_id")
    private Long userId; // Required for internal type

    @Email(message = "Valid email address is required")
    @Size(max = 255, message = "Email must not exceed 255 characters")
    private String email; // Required for external type

    @Size(max = 255, message = "Name must not exceed 255 characters")
    private String name; // Optional for external type

    @JsonProperty("brand_contact_id")
    private Long brandContactId; // Required for brand_contact type

    @NotNull(message = "Role is required")
    private HubParticipantRole role;

    public ParticipantInviteItem() {}

    public ParticipantInviteItem(String type, Long userId, String email, String name, 
                               Long brandContactId, HubParticipantRole role) {
        this.type = type;
        this.userId = userId;
        this.email = email;
        this.name = name;
        this.brandContactId = brandContactId;
        this.role = role;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getBrandContactId() {
        return brandContactId;
    }

    public void setBrandContactId(Long brandContactId) {
        this.brandContactId = brandContactId;
    }

    public HubParticipantRole getRole() {
        return role;
    }

    public void setRole(HubParticipantRole role) {
        this.role = role;
    }

    /**
     * Validates that participant type is valid.
     */
    @AssertTrue(message = "Invalid participant type. Must be 'internal', 'external', or 'brand_contact'")
    public boolean isValidParticipantType() {
        if (type == null) {
            return true; // Let @NotBlank handle null validation
        }
        String lowerType = type.toLowerCase();
        return "internal".equals(lowerType) || "external".equals(lowerType) || "brand_contact".equals(lowerType);
    }

    /**
     * Validates that userId is present for internal participants.
     */
    @AssertTrue(message = "User ID is required for internal participants")
    public boolean isValidInternalParticipant() {
        if (type == null) {
            return true; // Let other validators handle null validation
        }
        if ("internal".equals(type.toLowerCase())) {
            return userId != null;
        }
        return true; // Not internal type, validation passes
    }

    /**
     * Validates that email is present for external participants.
     */
    @AssertTrue(message = "Email is required for external participants")
    public boolean isValidExternalParticipant() {
        if (type == null) {
            return true; // Let other validators handle null validation
        }
        if ("external".equals(type.toLowerCase())) {
            return email != null && !email.trim().isEmpty();
        }
        return true; // Not external type, validation passes
    }

    /**
     * Validates that brandContactId is present for brand contact participants.
     */
    @AssertTrue(message = "Brand contact ID is required for brand contact participants")
    public boolean isValidBrandContactParticipant() {
        if (type == null) {
            return true; // Let other validators handle null validation
        }
        if ("brand_contact".equals(type.toLowerCase())) {
            return brandContactId != null;
        }
        return true; // Not brand_contact type, validation passes
    }

    /**
     * Validates that name is present for external participants.
     */
    @AssertTrue(message = "Name is required for external participants")
    public boolean isValidExternalParticipantName() {
        if (type == null) {
            return true; // Let other validators handle null validation
        }
        if ("external".equals(type.toLowerCase())) {
            return name != null && !name.trim().isEmpty();
        }
        return true; // Not external type, validation passes
    }

    /**
     * Validates that external participants cannot be assigned admin role.
     */
    @AssertTrue(message = "External participants cannot be assigned admin role")
    public boolean isValidExternalParticipantRole() {
        if (type == null || role == null) {
            return true; // Let other validators handle null validation
        }
        if ("external".equals(type.toLowerCase())) {
            return role != HubParticipantRole.admin;
        }
        return true; // Not external type, validation passes
    }

    /**
     * Validates that brand contact participants cannot be assigned admin role.
     */
    @AssertTrue(message = "Brand contact participants cannot be assigned admin role")
    public boolean isValidBrandContactParticipantRole() {
        if (type == null || role == null) {
            return true; // Let other validators handle null validation
        }
        if ("brand_contact".equals(type.toLowerCase())) {
            return role != HubParticipantRole.admin;
        }
        return true; // Not brand_contact type, validation passes
    }

    @Override
    public String toString() {
        return "ParticipantInviteItem{" +
                "type='" + type + '\'' +
                ", userId=" + userId +
                ", email='" + email + '\'' +
                ", name='" + name + '\'' +
                ", brandContactId=" + brandContactId +
                ", role=" + role +
                '}';
    }
}
