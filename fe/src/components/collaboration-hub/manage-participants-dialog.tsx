import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import {
  UserPlus,
  Users,
  Mail,
  MoreHorizontal,
  Trash2,
  RefreshCw,
  Crown,
  Edit,
  Camera,
  Eye,
  Building2,
  User,
  Check,
  ChevronsUpDown,
  Copy
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from "@/components/ui/dialog"
import { 
  Form, 
  FormControl, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage 
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select"
import { 
  Tabs, 
  TabsContent, 
  TabsList, 
  TabsTrigger 
} from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Badge } from "@/components/ui/badge"
import { Ava<PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from "@/components/ui/popover"

import { useIsMobile } from "@/hooks/use-mobile"
import { useBrandContacts } from "@/hooks/brands"
import {
  useHubParticipants,
  useInviteParticipants,
  useUpdateParticipantRole,
  useRemoveParticipant,
  useResendInvitation
} from "@/hooks/collaboration-hubs"
import { useHubPermissions } from "@/hooks/collaboration-hubs/use-hub-permissions"
import { useTranslations } from "@/lib/i18n/typed-translations"
import { cn } from "@/lib/utils"
import { toast } from "sonner"
import { fetchClient } from "@/lib/api/client"
import type {
  CollaborationHubResponse,
  HubParticipantResponse,
  ParticipantInviteItemRole,
  HubParticipantUpdateRoleRequestRole,
  ParticipantInviteItem
} from "./types"

interface ManageParticipantsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  hub: CollaborationHubResponse
}

// Form schema for inviting participants
const inviteParticipantSchema = z.object({
  type: z.enum(["external", "brand_contact"] as const),
  // External participant fields
  email: z.string().optional(),
  name: z.string().optional(),
  // Brand contact fields - now supports multiple selections
  brandContactIds: z.array(z.number()).optional(),
  // Common fields
  role: z.enum(["admin", "content_creator", "reviewer", "reviewer_creator"] as const),
}).superRefine((data, ctx) => {
  if (data.type === "external") {
    // For external users, email is required and must be valid
    if (!data.email || data.email.length === 0) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Email is required for external participants",
        path: ["email"]
      });
      return;
    }
    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(data.email)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Please enter a valid email address",
        path: ["email"]
      });
    }

    // For external users, name is required
    if (!data.name || data.name.trim().length === 0) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Name is required for external participants",
        path: ["name"]
      });
    }

    // External participants cannot be assigned admin role
    if (data.role === "admin") {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "External participants cannot be assigned admin role",
        path: ["role"]
      });
    }
  }

  if (data.type === "brand_contact") {
    // For brand contacts, at least one brandContactId is required
    if (!data.brandContactIds || data.brandContactIds.length === 0) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Please select at least one brand contact",
        path: ["brandContactIds"]
      });
    }

    // Brand contact participants cannot be assigned admin role
    if (data.role === "admin") {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Brand contact participants cannot be assigned admin role",
        path: ["role"]
      });
    }
  }
})

type InviteParticipantForm = z.infer<typeof inviteParticipantSchema>

export function ManageParticipantsDialog({
  open,
  onOpenChange,
  hub
}: ManageParticipantsDialogProps) {
  const { t, keys } = useTranslations()
  const isMobile = useIsMobile()
  const {
    hasAdminPermissions,
    canInviteParticipants,
    canEditParticipantRoles,
    canRemoveParticipants
  } = useHubPermissions(hub.id)

  // Set default tab based on permissions
  const [activeTab, setActiveTab] = useState(() =>
    hasAdminPermissions ? "invite" : "manage"
  )
  const [brandContactsOpen, setBrandContactsOpen] = useState(false)
  const [loadingParticipantId, setLoadingParticipantId] = useState<number | null>(null)
  const [loadingAction, setLoadingAction] = useState<'role' | 'delete' | 'resend' | 'copy' | null>(null)

  const form = useForm<InviteParticipantForm>({
    resolver: zodResolver(inviteParticipantSchema),
    defaultValues: {
      type: "brand_contact",
      email: undefined,
      name: undefined,
      brandContactIds: [],
      role: "content_creator"
    }
  })

  const watchedType = form.watch("type")

  // Fetch brand contacts from the hub's associated brand
  const { data: brandContacts, isLoading: brandContactsLoading } = useBrandContacts(
    hub.brandId,
    !!hub.brandId && open
  )

  // Fetch hub participants
  const { data: participantsData, isLoading: participantsLoading, refetch: refetchParticipants } = useHubParticipants(
    hub.id ?? null,
    { enabled: open }
  )

  // Mutation hooks for participant management
  const inviteParticipantsMutation = useInviteParticipants(hub.id!)
  const updateParticipantRoleMutation = useUpdateParticipantRole(hub.id!)
  const removeParticipantMutation = useRemoveParticipant(hub.id!)
  const resendInvitationMutation = useResendInvitation(hub.id!)

  // Clear dependent fields when type changes
  useEffect(() => {
    const currentRole = form.getValues("role")

    if (watchedType === "external") {
      form.setValue("brandContactIds", [])
      form.clearErrors("brandContactIds")

      // If current role is admin, reset to content_creator for external participants
      if (currentRole === "admin") {
        form.setValue("role", "content_creator")
        form.clearErrors("role")
      }
    } else if (watchedType === "brand_contact") {
      form.setValue("email", undefined)
      form.setValue("name", undefined)
      form.clearErrors("email")
      form.clearErrors("name")

      // If current role is admin, reset to content_creator for brand contact participants
      if (currentRole === "admin") {
        form.setValue("role", "content_creator")
        form.clearErrors("role")
      }
    }
  }, [watchedType, form])

  // Refetch participants when switching to manage tab
  useEffect(() => {
    if (activeTab === "manage" && open && hub.id) {
      // Use a small delay to ensure DOM is stable before refetching
      const timeoutId = setTimeout(() => {
        refetchParticipants()
      }, 100)

      return () => clearTimeout(timeoutId)
    }
  }, [activeTab, open, hub.id, refetchParticipants])

  // Reset form and tab when dialog closes
  useEffect(() => {
    if (!open) {
      // Reset to appropriate default tab based on permissions when dialog closes
      setActiveTab(hasAdminPermissions ? "invite" : "manage")
      // Reset form to prevent stale state
      form.reset()
      // Close brand contacts dropdown
      setBrandContactsOpen(false)
      // Reset loading states
      setLoadingParticipantId(null)
      setLoadingAction(null)
    }
  }, [open, form, hasAdminPermissions])

  // Helper function to check if a brand contact is already invited
  const isContactAlreadyInvited = (contactId: number): boolean => {
    if (!participants || participants.length === 0 || !brandContacts) return false

    // Find the brand contact by ID to get their email
    const contact = brandContacts.find(c => c.id === contactId)
    if (!contact) return false

    // Check if any participant has the same email as this brand contact
    return participants.some(participant =>
      participant.email?.toLowerCase() === contact.email?.toLowerCase()
    )
  }

  const onSubmit = async (data: InviteParticipantForm) => {

    try {
      // Prepare the invitation request
      let participants: ParticipantInviteItem[] = []

      if (data.type === "external") {
        // For external participants, create a single invitation
        participants = [{
          type: data.type,
          email: data.email,
          name: data.name,
          role: data.role as ParticipantInviteItemRole,
        }]
      } else if (data.type === "brand_contact" && data.brandContactIds) {
        // For brand contacts, create one invitation per selected contact
        participants = data.brandContactIds.map(contactId => ({
          type: data.type,
          brand_contact_id: contactId,
          role: data.role as ParticipantInviteItemRole,
        }))
      }

      const inviteRequest = { participants }

      await inviteParticipantsMutation.mutateAsync({
        params: {
          path: { hubId: hub.id! }
        },
        body: inviteRequest
      })

      // Use setTimeout to ensure DOM updates are processed before state changes
      setTimeout(() => {
        // Reset form on success
        form.reset()

        // Switch to manage tab to see the invited participant
        setActiveTab("manage")
      }, 50)

    } catch (__error) {
      // Error handling is managed by React Query
    }
  }

  const handleRoleUpdate = async (participantId: number, newRole: HubParticipantUpdateRoleRequestRole) => {
    setLoadingParticipantId(participantId)
    setLoadingAction('role')
    try {
      await updateParticipantRoleMutation.mutateAsync({
        params: {
          path: { hubId: hub.id!, participantId }
        },
        body: { role: newRole }
      })
    } catch (__error) {
      // Error handling is managed by React Query
    } finally {
      setLoadingParticipantId(null)
      setLoadingAction(null)
    }
  }

  const handleRemoveParticipant = async (participantId: number) => {
    setLoadingParticipantId(participantId)
    setLoadingAction('delete')
    try {
      await removeParticipantMutation.mutateAsync({
        params: {
          path: { hubId: hub.id!, participantId }
        }
      })
    } catch (__error) {
      // Error handling is managed by React Query
    } finally {
      setLoadingParticipantId(null)
      setLoadingAction(null)
    }
  }

  const handleResendInvitation = async (participantId: number) => {
    setLoadingParticipantId(participantId)
    setLoadingAction('resend')
    try {
      await resendInvitationMutation.mutateAsync({
        params: {
          path: { hubId: hub.id!, participantId }
        }
      })
    } catch (__error) {
      // Error handling is managed by React Query
    } finally {
      setLoadingParticipantId(null)
      setLoadingAction(null)
    }
  }

  const handleCopyInviteUrl = async (participantId: number) => {
    setLoadingParticipantId(participantId)
    setLoadingAction('copy')
    try {
      // Use the fetchClient directly for this one-off request
      const response = await fetchClient.GET('/api/hubs/{hubId}/participants/{participantId}/invite-url', {
        params: {
          path: { hubId: hub.id!, participantId }
        }
      })

      if (response.data?.invite_url) {
        await navigator.clipboard.writeText(response.data.invite_url)
        toast.success("Invite URL copied to clipboard")
      } else {
        toast.error("Failed to generate invite URL")
      }
    } catch (error) {
      console.error('Failed to copy invite URL:', error)
      toast.error("Failed to copy invite URL")
    } finally {
      setLoadingParticipantId(null)
      setLoadingAction(null)
    }
  }

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'admin':
        return <Crown className="h-4 w-4" />
      case 'content_creator':
        return <Camera className="h-4 w-4" />
      case 'reviewer':
        return <Eye className="h-4 w-4" />
      case 'reviewer_creator':
        return <Edit className="h-4 w-4" />
      default:
        return <Users className="h-4 w-4" />
    }
  }

  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case 'admin':
        return 'default'
      case 'content_creator':
        return 'secondary'
      case 'reviewer':
        return 'outline'
      case 'reviewer_creator':
        return 'outline'
      default:
        return 'secondary'
    }
  }

  const formatRole = (role: string) => {
    switch (role) {
      case 'admin':
        return t(keys.collaborationHubs.roles.admin)
      case 'content_creator':
        return t(keys.collaborationHubs.roles.content_creator)
      case 'reviewer':
        return t(keys.collaborationHubs.roles.reviewer)
      case 'reviewer_creator':
        return t(keys.collaborationHubs.roles.reviewer_creator)
      default:
        return role
    }
  }

  const getInitials = (name?: string) => {
    if (!name) return '?'
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  // Get available roles based on participant type
  const getAvailableRoles = (participantType: string) => {
    const allRoles = [
      { value: "content_creator", label: t(keys.collaborationHubs.roles.content_creator), icon: Camera },
      { value: "reviewer", label: t(keys.collaborationHubs.roles.reviewer), icon: Eye },
      { value: "reviewer_creator", label: t(keys.collaborationHubs.roles.reviewer_creator), icon: Edit },
      { value: "admin", label: t(keys.collaborationHubs.roles.admin), icon: Crown }
    ]

    // External and brand contact participants cannot have admin role
    if (participantType === "external" || participantType === "brand_contact") {
      return allRoles.filter(role => role.value !== "admin")
    }

    return allRoles
  }

  // Get participants from API data
  const participants: HubParticipantResponse[] = participantsData?.content || []
  const isLoading = participantsLoading || inviteParticipantsMutation.isPending

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className={cn(
        "max-w-4xl",
        isMobile && "h-[100dvh] max-h-[100dvh] w-[100vw] max-w-[100vw] rounded-none p-0"
      )}>
        <DialogHeader className={cn(
          isMobile && "pb-2 pt-4 px-4"
        )}>
          <DialogTitle className={cn(
            "flex items-center gap-2",
            isMobile && "text-base"
          )}>
            <Users className={cn("h-5 w-5", isMobile && "h-4 w-4")} />
            {t(keys.collaborationHubs.manageParticipants.title)}
          </DialogTitle>
          <DialogDescription className={cn(
            isMobile && "text-xs"
          )}>
            {t(keys.collaborationHubs.manageParticipants.description, { hubName: hub.name })}
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1">
          <TabsList className={cn(
            hasAdminPermissions ? "grid w-full grid-cols-2" : "grid w-full grid-cols-1",
            isMobile && "mx-4"
          )}>
            {canInviteParticipants && (
              <TabsTrigger value="invite" className="flex items-center gap-2">
                <UserPlus className="h-4 w-4" />
                <span className="hidden sm:inline">{t(keys.collaborationHubs.manageParticipants.inviteTab)}</span>
              </TabsTrigger>
            )}
            <TabsTrigger value="manage" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              <span className="hidden sm:inline">
                {hasAdminPermissions
                  ? t(keys.collaborationHubs.manageParticipants.manageTab, { count: participants.length })
                  : t(keys.collaborationHubs.manageParticipants.membersTab, { count: participants.length })
                }
              </span>
            </TabsTrigger>
          </TabsList>

          <ScrollArea className={cn(
            "flex-1 mt-4",
            isMobile ? "h-[calc(100dvh-160px)] px-4" : "max-h-[60vh]"
          )}>
            <TabsContent value="invite" className={cn(
              "space-y-4 px-1",
              isMobile && "px-0"
            )} key="invite-tab">
              <Form {...form}>
                <form
                  key={`invite-form-${activeTab}`}
                  onSubmit={form.handleSubmit(onSubmit)}
                  className="space-y-4"
                >
                  {/* Participant Type Selector */}
                  <FormField
                    control={form.control}
                    name="type"
                    render={({ field }) => (
                      <FormItem className="w-full">
                        <FormLabel>{t(keys.collaborationHubs.manageParticipants.participantType)}</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                          disabled={isLoading}
                        >
                          <FormControl>
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder={t(keys.collaborationHubs.manageParticipants.participantType)} />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="external">
                              <div className="flex items-center gap-2">
                                <User className="h-4 w-4" />
                                {t(keys.collaborationHubs.manageParticipants.externalUser)}
                              </div>
                            </SelectItem>
                            <SelectItem value="brand_contact">
                              <div className="flex items-center gap-2">
                                <Building2 className="h-4 w-4" />
                                {t(keys.collaborationHubs.manageParticipants.brandContact)}
                              </div>
                            </SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* External User Fields */}
                  {watchedType === "external" && (
                    <>
                      <FormField
                        control={form.control}
                        name="email"
                        render={({ field }) => (
                          <FormItem className="w-full">
                            <FormLabel>{t(keys.collaborationHubs.manageParticipants.emailAddress)}</FormLabel>
                            <FormControl>
                              <Input
                                className="w-full"
                                placeholder="<EMAIL>"
                                type="email"
                                {...field}
                                disabled={isLoading}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="name"
                        render={({ field }) => (
                          <FormItem className="w-full">
                            <FormLabel>{t(keys.collaborationHubs.manageParticipants.name)}</FormLabel>
                            <FormControl>
                              <Input
                                className="w-full"
                                placeholder="Participant Name"
                                {...field}
                                disabled={isLoading}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </>
                  )}

                  {/* Brand Contact Fields */}
                  {watchedType === "brand_contact" && (
                    <FormField
                      control={form.control}
                      name="brandContactIds"
                      render={({ field }) => (
                        <FormItem className="w-full">
                          <FormLabel>{t(keys.collaborationHubs.manageParticipants.brandContact)}</FormLabel>
                          <Popover open={brandContactsOpen} onOpenChange={setBrandContactsOpen}>
                            <PopoverTrigger>
                              <FormControl>
                                <Button
                                  type="button"
                                  variant="outline"
                                  role="combobox"
                                  aria-expanded={brandContactsOpen}
                                  className={cn(
                                    "w-full justify-between",
                                    !field.value?.length && "text-muted-foreground"
                                  )}
                                  disabled={isLoading || brandContactsLoading}
                                >
                                  {field.value?.length ? (
                                    <div className="flex flex-wrap gap-1">
                                      {field.value.slice(0, 2).map((contactId) => {
                                        const contact = brandContacts?.find(c => c.id === contactId)
                                        return contact ? (
                                          <Badge key={contactId} variant="secondary" className="text-xs">
                                            {contact.name}
                                          </Badge>
                                        ) : null
                                      })}
                                      {field.value.length > 2 && (
                                        <Badge variant="secondary" className="text-xs">
                                          +{field.value.length - 2} more
                                        </Badge>
                                      )}
                                    </div>
                                  ) : (
                                    brandContactsLoading
                                      ? t(keys.collaborationHubs.manageParticipants.loadingContacts)
                                      : t(keys.collaborationHubs.manageParticipants.selectContact)
                                  )}
                                  <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                                </Button>
                              </FormControl>
                            </PopoverTrigger>
                            <PopoverContent className="w-full p-0" align="start">
                              <Command>
                                <CommandInput placeholder={t(keys.collaborationHubs.manageParticipants.searchContacts)} />
                                <CommandList>
                                  <CommandEmpty>
                                    {hub.brandName ?
                                      t(keys.collaborationHubs.manageParticipants.noContactsForBrand, { brandName: hub.brandName }) :
                                      t(keys.collaborationHubs.manageParticipants.noContactsFound)
                                    }
                                  </CommandEmpty>
                                  <CommandGroup>
                                    {brandContacts?.map((contact) => {
                                      const isAlreadyInvited = isContactAlreadyInvited(contact.id)
                                      const isSelected = field.value?.includes(contact.id)

                                      return (
                                        <CommandItem
                                          key={contact.id}
                                          value={contact.name}
                                          disabled={isAlreadyInvited}
                                          onSelect={() => {
                                            if (isAlreadyInvited) return

                                            const currentValue = field.value || []

                                            if (isSelected) {
                                              field.onChange(currentValue.filter(id => id !== contact.id))
                                            } else {
                                              field.onChange([...currentValue, contact.id])
                                            }
                                          }}
                                          className={cn(
                                            isAlreadyInvited && "opacity-50 cursor-not-allowed"
                                          )}
                                        >
                                          <Check
                                            className={cn(
                                              "mr-2 h-4 w-4",
                                              isSelected ? "opacity-100" : "opacity-0"
                                            )}
                                          />
                                          <div className="flex items-center justify-between w-full">
                                            <div className="flex flex-col">
                                              <span className="font-medium">{contact.name}</span>
                                              <span className="text-xs text-muted-foreground">{contact.email}</span>
                                            </div>
                                            {isAlreadyInvited && (
                                              <Badge variant="outline" className="text-xs ml-2">
                                                Already invited
                                              </Badge>
                                            )}
                                          </div>
                                        </CommandItem>
                                      )
                                    })}
                                  </CommandGroup>
                                </CommandList>
                              </Command>
                            </PopoverContent>
                          </Popover>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}

                  <FormField
                    control={form.control}
                    name="role"
                    render={({ field }) => {
                      const availableRoles = getAvailableRoles(watchedType)
                      return (
                        <FormItem className="w-full">
                          <FormLabel>{t(keys.collaborationHubs.manageParticipants.role)}</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                            disabled={isLoading}
                          >
                            <FormControl>
                              <SelectTrigger className="w-full">
                                <SelectValue placeholder={t(keys.collaborationHubs.manageParticipants.selectRole)} />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {availableRoles.map((role) => {
                                const IconComponent = role.icon
                                return (
                                  <SelectItem key={role.value} value={role.value}>
                                    <div className="flex items-center gap-2">
                                      <IconComponent className="h-4 w-4" />
                                      {role.label}
                                    </div>
                                  </SelectItem>
                                )
                              })}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )
                    }}
                  />

                  <Alert>
                    {watchedType === "external" ? (
                      <>
                        <Mail className="h-4 w-4" />
                        <AlertDescription>
                          {t(keys.collaborationHubs.manageParticipants.externalInviteInfo)}
                        </AlertDescription>
                      </>
                    ) : (
                      <>
                        <Building2 className="h-4 w-4" />
                        <AlertDescription>
                          {t(keys.collaborationHubs.manageParticipants.brandContactInviteInfo, {
                            brandName: hub.brandName ? ` from ${hub.brandName}` : ''
                          })}
                        </AlertDescription>
                      </>
                    )}
                  </Alert>
                </form>
              </Form>
            </TabsContent>

            <TabsContent value="manage" className={cn(
              "space-y-4 px-1",
              isMobile && "px-0"
            )} key="manage-tab">
              {participantsLoading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                  <p className="mt-4 text-muted-foreground">{t(keys.collaborationHubs.manageParticipants.loadingParticipants)}</p>
                </div>
              ) : participants.length === 0 ? (
                <div className="text-center py-8">
                  <Users className="mx-auto h-12 w-12 text-muted-foreground" />
                  <h3 className="mt-4 text-lg font-semibold">{t(keys.collaborationHubs.manageParticipants.noParticipants)}</h3>
                  <p className="mt-2 text-muted-foreground">
                    {t(keys.collaborationHubs.manageParticipants.noParticipantsDescription)}
                  </p>
                  <Button
                    className="mt-4"
                    onClick={() => setActiveTab("invite")}
                  >
                    <UserPlus className="mr-2 h-4 w-4" />
                    {t(keys.collaborationHubs.manageParticipants.inviteParticipants)}
                  </Button>
                </div>
              ) : (
                <div className="space-y-3">
                  {participants.map((participant) => {
                    const isParticipantLoading = loadingParticipantId === participant.id
                    const isRoleLoading = isParticipantLoading && loadingAction === 'role'
                    const isDeleteLoading = isParticipantLoading && loadingAction === 'delete'
                    const isResendLoading = isParticipantLoading && loadingAction === 'resend'
                    const isCopyLoading = isParticipantLoading && loadingAction === 'copy'

                    return (
                      <div
                        key={participant.id}
                        className={cn(
                          "flex items-center justify-between p-4 border rounded-lg transition-opacity",
                          isParticipantLoading && "opacity-60"
                        )}
                      >
                        <div className="flex items-center gap-3">
                          <Avatar className="h-10 w-10">
                            <AvatarImage src="" />
                            <AvatarFallback>
                              {getInitials(participant.name)}
                            </AvatarFallback>
                          </Avatar>

                          <div className="flex-1">
                            <div className="flex items-center gap-2">
                              <p className="font-medium">{participant.name}</p>
                              {participant.isExternal && (
                                <Badge variant="outline" className="text-xs">
                                  {t(keys.collaborationHubs.manageParticipants.external)}
                                </Badge>
                              )}
                              {isDeleteLoading && (
                                <Badge variant="destructive" className="text-xs">
                                  Removing...
                                </Badge>
                              )}
                            </div>
                            <p className="text-sm text-muted-foreground">
                              {participant.email}
                            </p>
                          </div>
                        </div>

                        <div className="flex items-center gap-2">
                          <Badge variant={getRoleBadgeVariant(participant.role)}>
                            <div className="flex items-center gap-1">
                              {isRoleLoading ? (
                                <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-current" />
                              ) : (
                                getRoleIcon(participant.role)
                              )}
                              <span className="hidden sm:inline">
                                {isRoleLoading ? "Updating..." : formatRole(participant.role)}
                              </span>
                            </div>
                          </Badge>

                          <DropdownMenu>
                            <DropdownMenuTrigger>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-8 w-8 p-0"
                                disabled={!hasAdminPermissions ||
                                         isParticipantLoading ||
                                         updateParticipantRoleMutation.isPending ||
                                         removeParticipantMutation.isPending ||
                                         resendInvitationMutation.isPending}
                              >
                                {isParticipantLoading ? (
                                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current" />
                                ) : (
                                  <MoreHorizontal className="h-4 w-4" />
                                )}
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              {/* Only show admin option for internal participants */}
                              {!participant.isExternal && (
                                <DropdownMenuItem
                                  onClick={() => {
                                    handleRoleUpdate(participant.id!, "admin" as HubParticipantUpdateRoleRequestRole)
                                  }}
                                  disabled={!canEditParticipantRoles || participant.role === "admin" || isParticipantLoading}
                                >
                                  <Crown className="mr-2 h-4 w-4" />
                                  {t(keys.collaborationHubs.manageParticipants.makeAdmin)}
                                </DropdownMenuItem>
                              )}
                              <DropdownMenuItem
                                onClick={() => {
                                  handleRoleUpdate(participant.id!, "content_creator" as HubParticipantUpdateRoleRequestRole)
                                }}
                                disabled={!canEditParticipantRoles || participant.role === "content_creator" || isParticipantLoading}
                              >
                                <Camera className="mr-2 h-4 w-4" />
                                {t(keys.collaborationHubs.manageParticipants.contentCreator)}
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => {
                                  handleRoleUpdate(participant.id!, "reviewer" as HubParticipantUpdateRoleRequestRole)
                                }}
                                disabled={!canEditParticipantRoles || participant.role === "reviewer" || isParticipantLoading}
                              >
                                <Eye className="mr-2 h-4 w-4" />
                                {t(keys.collaborationHubs.manageParticipants.reviewer)}
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => {
                                  handleRoleUpdate(participant.id!, "reviewer_creator" as HubParticipantUpdateRoleRequestRole)
                                }}
                                disabled={!canEditParticipantRoles || participant.role === "reviewer_creator" || isParticipantLoading}
                              >
                                <Edit className="mr-2 h-4 w-4" />
                                {t(keys.collaborationHubs.manageParticipants.reviewerCreator)}
                              </DropdownMenuItem>
                              {participant.isExternal && hasAdminPermissions && (
                                <>
                                  <DropdownMenuItem
                                    onClick={() => {
                                      handleResendInvitation(participant.id!)
                                    }}
                                    disabled={isParticipantLoading}
                                  >
                                    {isResendLoading ? (
                                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2" />
                                    ) : (
                                      <RefreshCw className="mr-2 h-4 w-4" />
                                    )}
                                    {isResendLoading ? "Resending..." : t(keys.collaborationHubs.manageParticipants.resendInvitation)}
                                  </DropdownMenuItem>
                                  <DropdownMenuItem
                                    onClick={() => {
                                      handleCopyInviteUrl(participant.id!)
                                    }}
                                    disabled={isParticipantLoading}
                                  >
                                    {isCopyLoading ? (
                                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2" />
                                    ) : (
                                      <Copy className="mr-2 h-4 w-4" />
                                    )}
                                    {isCopyLoading ? "Copying..." : "Copy Invite URL"}
                                  </DropdownMenuItem>
                                </>
                              )}
                              {canRemoveParticipants && (
                                <DropdownMenuItem
                                  onClick={() => {
                                    handleRemoveParticipant(participant.id!)
                                  }}
                                  className="text-destructive focus:text-destructive"
                                  disabled={isParticipantLoading}
                                >
                                  {isDeleteLoading ? (
                                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2" />
                                  ) : (
                                    <Trash2 className="mr-2 h-4 w-4" />
                                  )}
                                  {isDeleteLoading ? "Removing..." : t(keys.collaborationHubs.manageParticipants.remove)}
                                </DropdownMenuItem>
                              )}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </div>
                    )
                  })}
                </div>
              )}
            </TabsContent>
          </ScrollArea>
        </Tabs>

        <DialogFooter className={cn(
          "flex-col sm:flex-row gap-2",
          isMobile && "pt-2 pb-4 px-4"
        )}>
          {activeTab === "invite" && canInviteParticipants && (
            <>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isLoading}
              >
                {t(keys.collaborationHubs.manageParticipants.cancel)}
              </Button>
              <Button
                type="button"
                onClick={() => {
                  form.handleSubmit(onSubmit)()
                }}
                disabled={isLoading}
              >
                {isLoading ? t(keys.collaborationHubs.manageParticipants.sendingInvitation) : t(keys.collaborationHubs.manageParticipants.sendInvitation)}
              </Button>
            </>
          )}
          {activeTab === "manage" && (
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
            >
              {t(keys.collaborationHubs.manageParticipants.close)}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
